<script setup lang="ts">
import { NLayoutSider } from 'naive-ui'
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import cache from '@/plugins/cache'
import '@/assets/img/fdqf-logo.png'
import '@/assets/img/kyy-logo.png'
import '@/assets/img/cgzh-logo.png'
import '@/assets/img/qxb-logo.png'
import '@/assets/img/kph-logo.png'
import '@/assets/img/tqap-logo.png'
const appStore = useAppStore()
const router = useRouter()
const route = useRoute()
const currentSub = ref([]) // 假设 currentSub 这样定义，如果实际不同需调整
const goActive = ref(9)
const hasSub = ref(true)
const toName = router.currentRoute.value.name

const subMenu = ref(appStore.subMenuData)
watch(
  () => appStore.subMenuData,
  (newData) => {
    // 当 appStore 内的 subMenuData 改变时，更新 subMenu 的值
    subMenu.value = newData || []
    currentSub.value = newData || []
    hasSub.value = currentSub.value.length > 0
    console.log('这是在监听里面 ===', route.query)
    // debugger
    if (route.query.origin) {
      const zIndex = currentSub?.value.findIndex(item =>
        item.path.includes(route.query.code),
      )
      if (zIndex > -1)
        menuItemClick(currentSub?.value[zIndex], 'isN')
    }
    else {
      currentSub.value.length > 0 && menuItemClick(currentSub?.value[0], 'isN')
    }

    // goActive.value = currentSub?.value[0].path
    // router.replace({
    //   path: router.currentRoute.value.path,
    //   query: JSON.parse(currentSub?.value[0].query),
    // })
    // router.push({
    //   path: currentSub?.value[0].path,
    //   query: JSON.parse(currentSub?.value[0].query),
    //   replace: true,
    // })
  },
)

watch(currentSub, (newValue, oldValue) => {
  // console.log("currentSub 从", oldValue, "变为", newValue);
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
watch(hasSub, (newValue, oldValue) => {
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
watch(goActive, (newValue, oldValue) => {
  // 这里可以添加更新视图的逻辑，比如触发组件重新渲染等
})
const menuItemClick = (item, type) => {
  cache.session.set('appKey', item.menuParam1 || '')
  goActive.value = item.path
  if (item.type === 'chain' || item.path.startsWith('http'))
    window.open(item.path, '_blank')

  if (item.query) {
    router.push({
      name: item.component,
      query: JSON.parse(item.query),
    })
  }
  else {
    router.push({
      name: item.component,
    })
  }
}

onMounted(() => {
  const existingScript = document.querySelector('#iconfont-script')
  if (existingScript)
    return
  const script = document.createElement('script')
  script.id = 'iconfont-script'
  script.src = 'https://at.alicdn.com/t/c/font_4933970_349n7c3p0rs.js'
  document.body.appendChild(script)
  // 在组件卸载时取消监听
  return () => {
    // unregister()
  }
})
</script>

<template>
  <NLayoutSider
    v-if="hasSub"
    :collapsed="collapsed"
    content-style="padding: 18px;"
    collapse-mode="width"
    :collapsed-width="10"
    :width="240"
    show-trigger="arrow-circle"
  >
    <div v-for="(item, index) in currentSub" :key="index" class="relative">
      <div
        class="menu-item"
        :class="[goActive === item.path ? 'active' : '']"
        @click="menuItemClick(item)"
      >
        <div class="menu-item-title flex">
          <span class="menu-item-icon mr-1 pt-1">
            <img
              v-if="item.meta.icon.indexOf('images') > -1"
              :src="item.meta.icon"
              alt=""
              class="item-img"
            >
            <svg v-else class="icon" aria-hidden="true">
              <use :xlink:href="`#${item.meta.icon}`" />
            </svg>
          </span>
          <div class="menu-item-text">
            {{ item.meta.title }}
          </div>
        </div>
      </div>
    </div>
  </NLayoutSider>
</template>

<style scoped lang="less">
.n-layout-sider {
  background: #f9fbff;
  .icon {
    width: 26px;
    height: 26px;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  .menu-item-title {
    flex-direction: row;
    justify-content: left;
    align-items: center;
  }
  .item-img {
    width: 26px;
  }
  .menu-item {
    background: #f6f7fc;
    border-radius: 5px;
    padding: 7px 10px;
    margin-bottom: 5px;
    position: relative;
    cursor: pointer;
    &.active {
      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: calc(50% - 10px);
        width: 0;
        height: 0;
        border-top: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-right: 8px solid #2355f4;
      }
    }
  }
}
</style>
