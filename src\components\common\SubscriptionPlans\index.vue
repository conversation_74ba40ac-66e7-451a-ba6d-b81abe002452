<script setup>
import { ref } from 'vue'
import { NButton, NCard, NIcon, useMessage } from 'naive-ui'
import { CheckmarkCircle } from '@vicons/ionicons5'

const message = useMessage()

// 订阅计划数据
const plans = ref([
  {
    id: 'basic',
    name: '基础套餐',
    price: '4.9',
    originalPrice: '9.9',
    period: '月',
    popular: false,
    features: [
      '每日100次对话',
      '基础AI模型',
      '标准响应速度',
      '邮件客服支持'
    ],
    buttonText: '立即充值',
    color: '#52c41a',
    bgGradient: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)'
  },
  {
    id: 'standard',
    name: '标准套餐',
    price: '9.9',
    originalPrice: '19.9',
    period: '月',
    popular: true,
    features: [
      '每日500次对话',
      '高级AI模型',
      '优先响应速度',
      '24小时客服支持',
      '自定义角色'
    ],
    buttonText: '立即充值',
    color: '#1677ff',
    bgGradient: 'linear-gradient(135deg, #1677ff 0%, #69b1ff 100%)'
  },
  {
    id: 'premium',
    name: '专业套餐',
    price: '14.9',
    originalPrice: '29.9',
    period: '月',
    popular: false,
    features: [
      '无限次对话',
      '最新AI模型',
      '极速响应',
      '专属客服',
      '高级功能',
      '数据导出'
    ],
    buttonText: '立即充值',
    color: '#722ed1',
    bgGradient: 'linear-gradient(135deg, #722ed1 0%, #b37feb 100%)'
  }
])

// 处理订阅
const handleSubscribe = (plan) => {
  message.success(`选择了${plan.name}，价格：¥${plan.price}/${plan.period}`)
  // 这里可以添加实际的支付逻辑
}
</script>

<template>
  <div class="subscription-plans">
    <div class="plans-header">
      <h2 class="plans-title">选择您的订阅计划</h2>
      <p class="plans-subtitle">解锁更多AI功能，提升您的工作效率</p>
    </div>
    
    <div class="plans-container">
      <div 
        v-for="plan in plans" 
        :key="plan.id" 
        class="plan-card"
        :class="{ 'popular': plan.popular }"
      >
        <!-- 热门标签 -->
        <div v-if="plan.popular" class="popular-badge">
          <span>最受欢迎</span>
        </div>
        
        <!-- 卡片头部 -->
        <div class="plan-header" :style="{ background: plan.bgGradient }">
          <h3 class="plan-name">{{ plan.name }}</h3>
          <div class="plan-pricing">
            <div class="price-main">
              <span class="currency">¥</span>
              <span class="price">{{ plan.price }}</span>
              <span class="period">/{{ plan.period }}</span>
            </div>
            <div class="price-original">
              原价 ¥{{ plan.originalPrice }}
            </div>
          </div>
        </div>
        
        <!-- 功能列表 -->
        <div class="plan-features">
          <div 
            v-for="feature in plan.features" 
            :key="feature" 
            class="feature-item"
          >
            <NIcon class="feature-icon" :color="plan.color">
              <CheckmarkCircle />
            </NIcon>
            <span class="feature-text">{{ feature }}</span>
          </div>
        </div>
        
        <!-- 订阅按钮 -->
        <div class="plan-action">
          <NButton 
            :type="plan.popular ? 'primary' : 'default'"
            size="large"
            block
            class="subscribe-btn"
            :style="plan.popular ? { background: plan.bgGradient, border: 'none' } : {}"
            @click="handleSubscribe(plan)"
          >
            {{ plan.buttonText }}
          </NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.subscription-plans {
  padding: 32px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.plans-header {
  text-align: center;
  margin-bottom: 48px;
}

.plans-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plans-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.6;
}

.plans-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  align-items: stretch;
}

.plan-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border: 2px solid transparent;
}

.plan-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.popular {
  border-color: #1677ff;
  transform: scale(1.05);
}

.plan-card.popular:hover {
  transform: scale(1.05) translateY(-8px);
}

.popular-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 6px 20px;
  border-radius: 0 0 12px 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(238, 90, 36, 0.3);
}

.plan-header {
  padding: 32px 24px;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.plan-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 16px 0;
  position: relative;
  z-index: 1;
}

.plan-pricing {
  position: relative;
  z-index: 1;
}

.price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8px;
}

.currency {
  font-size: 20px;
  font-weight: 600;
  margin-right: 4px;
}

.price {
  font-size: 48px;
  font-weight: 800;
  line-height: 1;
}

.period {
  font-size: 16px;
  font-weight: 500;
  margin-left: 4px;
}

.price-original {
  font-size: 14px;
  opacity: 0.8;
  text-decoration: line-through;
}

.plan-features {
  padding: 32px 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }
.feature-item:nth-child(5) { animation-delay: 0.5s; }
.feature-item:nth-child(6) { animation-delay: 0.6s; }

.feature-icon {
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.feature-text {
  font-size: 15px;
  color: #333;
  line-height: 1.5;
}

.plan-action {
  padding: 0 24px 32px;
}

.subscribe-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .subscription-plans {
    padding: 24px 16px;
  }
  
  .plans-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .plan-card.popular {
    transform: none;
  }
  
  .plan-card.popular:hover {
    transform: translateY(-4px);
  }
  
  .plans-title {
    font-size: 24px;
  }
  
  .plan-header {
    padding: 24px 20px;
  }
  
  .price {
    font-size: 36px;
  }
  
  .plan-features {
    padding: 24px 20px;
  }
  
  .plan-action {
    padding: 0 20px 24px;
  }
}
</style>
