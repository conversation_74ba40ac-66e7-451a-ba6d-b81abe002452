// 图表动画配置类型定义
export interface AnimationConfig {
  // 全局动画开关
  enabled: boolean
  
  // 性能相关配置
  performance: {
    maxFPS: number
    enableGPUAcceleration: boolean
    autoDowngrade: boolean
    batteryOptimization: boolean
  }
  
  // 基础动画配置
  basic: {
    duration: number
    easing: string
    delay: number
  }
  
  // 高级动画特效
  effects: {
    particle: {
      enabled: boolean
      count: number
      speed: number
      color: string
    }
    flow: {
      enabled: boolean
      speed: number
      opacity: number
    }
    breath: {
      enabled: boolean
      scale: number
      duration: number
    }
    glow: {
      enabled: boolean
      intensity: number
      color: string
    }
  }
  
  // 移动端配置
  mobile: {
    simplifiedAnimations: boolean
    reducedParticles: boolean
    lowerFrameRate: boolean
  }
}

// 图表类型枚举
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  GAUGE = 'gauge',
  SCATTER = 'scatter',
  RADAR = 'radar'
}

// 动画类型枚举
export enum AnimationType {
  INIT = 'init',
  UPDATE = 'update',
  HOVER = 'hover',
  CLICK = 'click',
  CONTINUOUS = 'continuous'
}

// 性能监控数据
export interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  animationCount: number
  timestamp: number
}

// 图表动画事件
export interface ChartAnimationEvent {
  type: AnimationType
  chartId: string
  duration: number
  data?: any
}
