<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue'
import {
  NButton,
  NDataTable,
  NInput,
  NModal,
  NTabPane,
  NTabs,
  NTag,
  useMessage,
} from 'naive-ui'
import to from 'await-to-js'
import {
  listPlan,
  redeemKey,
} from '@/api/pay'
import { modelList } from '@/api/model'
import { t } from '@/locales'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import PremiumPaymentModal from '@/components/common/PremiumPaymentModal/index.vue'

const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const active = ref(false)
const loading = ref<boolean>(false)
onMounted(() => {
  fetchData1()
  // 查询套餐信息
  fetchPackages()
})

interface Props {
  visible: boolean
  title: string
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
}

const { isMobile } = useBasicLayout()
const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})

const message = useMessage()

// 卡密信息
const redeem = ref('')
// 兑换卡密
async function handleRedeemKey(money: string, name: string) {
  const [err, result] = await to(
    redeemKey({
      code: redeem.value,
      id: '',
      userId: '',
      amount: 0,
      status: '',
      balanceBefore: 0,
      balanceAfter: 0,
      remark: '',
    }),
  )
  if (err)
    message.error(err.message)
  else message.success('兑换成功!')

  console.log('result===', result)
}

// 支付弹窗相关状态
const paymentVisible = ref(false)
const currentOrderInfo = ref({
  money: '0',
  name: '',
  description: '',
  id: '',
})

async function addOrderFuc(plan: any) {
  console.log('plan===', plan)

  // 设置订单信息并显示支付弹窗
  currentOrderInfo.value = {
    money: plan.price,
    name: plan.name,
    description: plan.planDetail || '专业AI套餐服务',
    id: plan.id,
  }

  paymentVisible.value = true
}

// 支付成功回调
function handlePaymentSuccess() {
  message.success('支付成功！套餐已激活')
  // 这里可以添加刷新用户信息等逻辑
}

// 支付取消回调
function handlePaymentCancel() {
  message.info('支付已取消')
}

const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  },
})

const createColumns = () => {
  return [
    ...(false
      ? [
          {
            title: '主键',
            key: 'id',
            width: 80,
            ellipsis: true,
          },
        ]
      : []),
    {
      title: t('model.name'),
      key: 'modelDescribe',
      width: 60,
    },
    {
      title: t('model.price'),
      key: 'modelPrice',
      width: 60,
    },
    {
      title: t('model.type'),
      key: 'modelType',
      width: 20,
      render: (row: any) => {
        let text, type
        switch (row.modelType) {
          case '1':
            text = 'Token billing'
            type = 'success' // 绿色标签
            break
          case '2':
            text = 'Frequency billing'
            type = 'info' // 蓝色标签
            break
          default:
            text = 'Unknown billing'
            type = 'default' // 默认灰色标签
        }
        // 直接使用导入的 NTag 组件，设置相应的属性
        return h(
          NTag,
          {
            type,
            size: 'medium',
            round: true,
          },
          {
            default: () => text,
          },
        )
      },
    },
    {
      title: t('model.remark'),
      key: 'remark',
      width: 200,
    },
  ]
}

const tableData = ref([])

const fetchData1 = async () => {
  try {
    // 发起一个请求
    const [err, result] = await to(modelList('chat'))

    if (err)
      message.error(err.message)
    else tableData.value = result.data
  }
  catch (error) {
    console.error('Error fetching data:', error)
  }
}

const columns = ref(createColumns())
const subscriptionPlans = ref([])
const subscriptionPlansYear = ref([])
async function fetchPackages() {
  // 发起一个请求
  const [err, result] = await to(listPlan())
  if (err) {
    message.error(err.message)
  }
  else {
    subscriptionPlans.value = result.data.filter(e => e.duration < 32)
    subscriptionPlansYear.value = result.data.filter(e => e.duration > 32)
  }
}
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    :style="{
      maxWidth: '1100px',
      position: 'fixed',
      left: '50%',
      top: isMobile ? '2vh' : '10vh',
      transform: 'translate(-50%, 0%)',
    }"
    :class="[isMobile ? 'plan-draw mobile-plan-draw' : 'plan-draw']"
  >
    <NTabs
      type="line"
      size="large"
      :tabs-padding="20"
      pane-style="padding: 20px;"
      class="plan-tabs"
    >
      <NTabPane :name="$t('setting.plan')">
        <!-- <div class="change-combo">
					<span :class="[active ? '' : 'active-span']">Monthly</span>
					<n-switch style="zoom: 1.5" v-model:value="active"  size="large"/>
					<span :class="[active ? 'active-span' : '']">Annually</span>
				</div> -->
        <div
          v-if="active ? subscriptionPlansYear.length : subscriptionPlans.length"
          style="display: flex; overflow: auto"
          class="plan-content"
        >
          <div
            v-for="(plan, index) in active
              ? subscriptionPlansYear.slice(0, 3)
              : subscriptionPlans.slice(0, 3)"
            :key="index"
            class="plan-item"
          >
            <div class="header">
              <span class="title">{{ plan.name }}</span>
              <p style="float: right">
                <span class="price">{{ $t("store.unit") }}{{ plan.price }}</span>
                <span class="date" />
              </p>
            </div>
            <div class="content">
              <div
                v-for="(feature, fIndex) in plan.planDetail.split(',')"
                :key="fIndex"
                class="option-item"
              >
                <div class="quanquan">
                  <IconSvg icon="gou" />
                </div>
                <p>{{ feature }}</p>
              </div>
            </div>
            <NButton class="footer" :loading="loading" @click="addOrderFuc(plan)">
              <IconSvg icon="add" />{{ $t("setting.recharge") }}
            </NButton>
          </div>
        </div>
      </NTabPane>
     
    </NTabs>
  </NModal>

  <!-- 支付弹窗 -->
  <PremiumPaymentModal
    v-model:visible="paymentVisible"
    :order-info="currentOrderInfo"
    @success="handlePaymentSuccess"
    @cancel="handlePaymentCancel"
  />
</template>

<style scoped>
.n-gradient-text {
  font-size: 18px;
}

.outer-container {
  display: flex;
  /* 设置外部容器为 Flexbox */
  justify-content: center;
  /* 水平居中对齐子项目 */
}
</style>
