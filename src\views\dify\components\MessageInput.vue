<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElButton, ElIcon, ElInput, ElMessage, ElUpload } from 'element-plus'
import { CircleCloseFilled, Paperclip, Promotion, UploadFilled, VideoPause } from '@element-plus/icons-vue'
import { formatFileSize, getFileType } from '@/utils/functions/index'
import { useUserStore } from '@/store'
import { useChatStore } from '@/store/chat'
import cache from '@/plugins/cache'
import { chatApi } from '@/api/difychat'
import { chatCost } from '@/api/user'

const props = defineProps<{
  disabled: boolean
  stopTask: boolean
}>()
const emit = defineEmits<{
  (e: 'send', value: string): void
  (e: 'task', value: boolean): void
}>()
const userStore = useUserStore()
const chatStore = useChatStore()
const userInfo = computed(() => userStore.userInfo)
const appInfo = computed(() => chatStore.appInfo)
const appParams = computed(() => chatStore.appParams)
const chatComplete = computed(() => chatStore.chatComplete)

const inputText = ref('')
const inputRef = ref<InstanceType<typeof ElInput> | null>(null)

const uploadFile = ref<InstanceType<typeof ElUpload> | any>([])
const fileList = ref([])
const actionUrl = computed(() => {
  return `${import.meta.env.VITE_DIFY_API_BASE_URL}/files/upload`
})
const remote_url = ref('')

// 设置请求头（含认证信息）
const headers = ref({
  Authorization: `Bearer ${import.meta.env.VITE_DIFY_API_TOKEN}`, // 替换为你的Dify API Key
})
// 如果需要额外参数（根据Dify API要求）
const extraParams = ref({
  user: userInfo.value?.userId, // 可选：用户标识
  // type: 'knowledge'   // 可选：上传类型
})
// 上传前的校验
const beforeUpload = (file: any) => {
  const allTypes = {
    document: [
      '.txt',
      '.md',
      '.markdown',
      '.pdf',
      '.html',
      '.xlsx',
      '.xls',
      '.docx',
      '.csv',
      '.eml',
      '.msg',
      '.pptx',
      '.xml',
      '.epub',
    ],
    image: ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
    video: ['.mp3', '.m4a', '.wav', '.amr', '.mpga'],
    audio: ['.mp4', '.mov', '.mpeg', '.webm'],
  }
  const allowedTypes: { [s: string]: any } = []
  const allowed_file_types = appParams.value?.file_upload?.allowed_file_types || []
  const allowed_file_extensions
    = appParams.value?.file_upload?.allowed_file_extensions || []

  if (!allowed_file_types?.length && !allowed_file_extensions?.length) {
    ElMessage.error('未配置允许的文件类型!')
    return false
  }

  if (allowed_file_types?.length) {
    allowed_file_types.forEach((item: any) => {
      if (item === 'document')
        allowedTypes.push(...allowedTypes, ...allTypes.document)
      if (item === 'image')
        allowedTypes.push(...allowedTypes, ...allTypes.image)
      if (item === 'video')
        allowedTypes.push(...allowedTypes, ...allTypes.video)
      if (item === 'audio')
        allowedTypes.push(...allowedTypes, ...allTypes.audio)
    })
  }
  let limitSize = 15
  const extension = file.name.split('.').pop().toLowerCase()
  if (getFileType(extension) === 'document')
    limitSize = appParams.value?.file_upload?.file_size_limit || 15
  else if (getFileType(extension) === 'image')
    limitSize = appParams.value?.file_upload?.image_file_size_limit || 10
  else if (getFileType(extension) === 'audio')
    limitSize = appParams.value?.file_upload?.audio_file_size_limit || 50
  else if (getFileType(extension) === 'video')
    limitSize = appParams.value?.file_upload?.video_file_size_limit || 20
  const isLt15M = file.size / 1024 / 1024 < limitSize
  if (!isLt15M) {
    ElMessage.error(`上传内容大小不能超过 ${limitSize}MB!`)
    return false
  }
  // if (!isAllowed) {
  //   ElMessage.error(`仅支持以下文件类型: ${allowedTypes}`);
  //   return false;
  // }
  // 3. 双重校验：检查文件扩展名（某些浏览器可能报告不正确的 MIME 类型）
  const fileExtension = `.${file.name.split('.').pop().toLowerCase()}`

  const isExtensionValid = allowedTypes.includes(fileExtension)

  if (!isExtensionValid) {
    ElMessage.error(`仅支持以下文件类型: ${allowedTypes.join(', ')}`)
    return false
  }

  return true
}
// 上传成功回调
const handleSuccess = (response, file) => {
  // Dify 成功响应示例: { "id": "file-123", "name": "example.pdf", ... }
  if (response.id) {
    // ElMessage.success(`${file.name} 上传成功!`);
    uploadFile.value.push(response)
  }
  else {
    ElMessage.error('上传成功但返回数据异常')
  }
}

// 上传失败回调
const handleError = (error, file) => {
  console.error('上传错误:', error)
  ElMessage.error(`${file.name} 上传失败: ${error.message || '服务器错误'}`)
}

const delUploadFile = (index) => {
  uploadFile.value.splice(index, 1)
}

const placeholder = computed(() => {
  return props.disabled ? '等待响应中...' : '输入您的问题...'
})

const handleEnter = (e: Event | KeyboardEvent) => {
  // Only send on Enter without shift key
  if (!(e instanceof KeyboardEvent) || (!e.shiftKey && inputText.value.trim()))
    handleSend()
}

const chatCostFuc = (inputText: string) => {
  const param = {
    messages: [
      {
        role: 'user',
        content: inputText,
      },
    ],
    model: 'dall-e-3',
    prompt: appParams.value?.opening_statement,
    role: 'user',
    agentName: appInfo.value?.name,
  }
  chatCost(param).then((res) => {
    console.log('chatCostFuc res: ', res)
  })
}
const handleTaskFuc = (val: boolean) => {
  emit('task', val)
}

const handleSend = () => {
  if (inputText.value.trim() && !props.disabled) {
    // chatCostFuc(inputText.value)
    emit('send', inputText.value, uploadFile.value)
    handleTaskFuc(true)
    inputText.value = ''
    uploadFile.value = []
    setTimeout(() => {
      if (inputRef.value && inputRef.value.$el) {
        const textarea = inputRef.value.$el.querySelector('textarea')
        if (textarea)
          textarea.focus()
      }
    }, 10)
  }
}

const stopTaskFuc = () => {
  const param = {
    user: userInfo.value?.userId,
  }
  chatApi.stopTask(cache.session.get('taskId'), param).then((res) => {
    // ElMessage.success('已停止响应')
    handleTaskFuc(false)
  })
}
watch(chatComplete, (newVal) => {
  if (newVal) {
    cache.session.remove('taskId')
    handleTaskFuc(false)
  }
})

onMounted(() => {
  if (inputRef.value && inputRef.value.$el) {
    const textarea = inputRef.value.$el.querySelector('textarea')
    if (textarea)
      textarea.focus()
  }
})
</script>

<template>
  <div class="message-input shadow-md">
    <div v-if="props.stopTask" class="stop-task" @click="stopTaskFuc">
      <ElIcon class="mr-1">
        <VideoPause />
      </ElIcon> 停止响应
    </div>
    <div v-if="uploadFile?.length" class="upload-file flex flex-wrap gap-2">
      <div
        v-for="(item, index) in uploadFile"
        :key="item.id"
        class="group/file-item relative h-[68px] w-[144px] rounded-lg border-[0.5px] border-components-panel-border bg-components-card-bg p-2 shadow-xs"
      >
        <span class="del-file-icon" @click="delUploadFile(index)">
          <ElIcon><CircleCloseFilled /></ElIcon>
        </span>
        <!-- <img
          v-if="item?.mime_type.indexOf('image') !== -1"
          :src="item?.url"
          class="image-file"
        ></img> -->
        <div>
          <div
            class="system-xs-medium mb-1 line-clamp-2 h-8 cursor-pointer break-all text-text-tertiary"
          >
            {{ item?.name || "上传文件" }}
          </div>
          <div class="relative flex items-center system-2xs-medium">
            {{ item?.extension.toUpperCase() }}
            <div class="mx-1">
              ·
            </div>
            {{ formatFileSize(item?.size || 0) }}
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between">
      <div class="relative flex w-full grow items-center">
        <ElInput
          ref="inputRef"
          v-model="inputText"
          :placeholder="placeholder"
          :disabled="disabled"
          clearable
          type="textarea"
          resize="none"
          :rows="1"
          autosize
          class="custom-input"
          @keydown.enter.prevent="handleEnter"
        />
      </div>
      <div class="flex shrink-0 items-center justify-end">
        <el-popover v-if="appParams?.file_upload?.enabled" placement="top" :width="280" trigger="click">
          <template #reference>
            <ElButton
              type="text"
              :loading="disabled"
              :disabled="uploadFile?.length === appParams?.file_upload?.number_limits"
              class="input-button upload-btn"
            >
              <ElIcon><Paperclip /></ElIcon>
            </ElButton>
          </template>
          <div>
            <!-- <div class="origin-url">
              <ElInput
                v-model="remote_url"
                placeholder="输入文件链接"
                class="input-with-select"
              >
                <template #append>
                  <span class="px-2 remote-url-btn">
                    好的
                  </span>
                </template>
              </ElInput>
            </div>
            <el-divider>
              or
            </el-divider> -->

            <ElUpload
              v-if="appParams?.file_upload?.enabled"
              :action="actionUrl"
              :headers="headers"
              :data="extraParams"
              :on-success="handleSuccess"
              :on-error="handleError"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :file-list="fileList"
              class="w-full"
              :multiple="true"
              :limit="appParams?.file_upload?.number_limits || 1"
            >
              <template #trigger>
                <ElButton
                  :icon="UploadFilled" :loading="disabled"
                  :disabled="uploadFile?.id" type="primary" plain class="w-full"
                >
                  本地上传
                </ElButton>
              </template>
            </ElUpload>
          </div>
        </el-popover>
        <ElButton
          type="primary"
          :loading="disabled"
          :disabled="!inputText.trim()"
          class="send-button"
          @click="handleSend"
        >
          <ElIcon><Promotion /></ElIcon>
        </ElButton>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.message-input {
  width: 100%;
  position: relative;
  border-radius: 8px;
  padding-right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  padding: 5px 10px;
  .stop-task{
    position: absolute;
    top: -35px;
    left: 48%;
    background: #fff;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
  }
  .upload-file {
    .del-file-icon {
      right: -5px;
      position: absolute;
      color: #999;
      top: -5px;
      font-size: 16px;
      cursor: pointer;
    }
    .system-xs-medium {
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      color: #676f83;
    }
    .system-2xs-medium {
      font-size: 10px;
      font-weight: 500;
      line-height: 12px;
      color: #676f83;
    }
  }
}
.remote-url-btn{
  cursor: pointer;
  &:hover{
    color: #1c64f2;
    background: #ecf5ff;
  }
}

.custom-input {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

:deep(.el-upload){
  width: 100%;
}
:deep(.el-input__wrapper) {
  /* border-radius: var(--radius-lg);
  padding-right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-200);
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white; */
}

:deep(.el-input__wrapper:hover),
:deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  /* border-color: var(--primary-color); */
}

:deep(.el-textarea__inner) {
  resize: none;
  padding: 12px 16px;
  min-height: 24px !important;
  max-height: 150px !important;
  line-height: 1.5;
  font-size: 1rem;
  color: var(--neutral-800);
  box-shadow: none;
}

:deep(.el-input-group__append) {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
  padding: 0;
  background-color: var(--primary-color);
  border: none;
}

.input-hint {
  color: var(--neutral-500);
  font-size: 0.7rem;
  margin-top: 6px;
  text-align: right;
  padding-right: 8px;
}
.input-button {
  padding: 0 16px;
  height: 40px;
  width: 40px;
  border-radius: 10px;
  margin-right: 10px;
  margin-top: 1px;
  .el-icon {
    font-size: 1.5rem;
    color: #666;
  }
  &:hover {
    background: #eee;
    .el-icon {
      color: #111;
    }
  }
}
.upload-btn {
  .el-icon {
    font-weight: bold;
    transform: rotate(-46deg);
  }
}

.send-button {
  border: none;
  background-color: #1c64f2;
  color: white;
  height: 40px;
  width: 48px;
  padding: 0 16px;
  font-size: 0.9rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.send-button:hover {
  background-color: darken(#1c64f2, 5%);
}

.send-button:disabled {
  /* background-color: var(--primary-light); */
  color: white;
}

.send-button .el-icon {
  font-size: 1.5rem;
}
</style>
