<script setup lang="ts">
import type { Component } from 'vue'
import { computed, defineAsyncComponent, h, onMounted, ref } from 'vue'
import {
  NAvatar,
  NButton,
  NIcon,
  NLayoutHeader,
  NMenu,
  NModal,
  useMessage,
} from 'naive-ui'
import { useRouter } from 'vue-router'
import to from 'await-to-js'
import { LogOut as out, QrCode as qrcode, Settings as settings, Storefront as storefront } from '@vicons/ionicons5'
import html2canvas from 'html2canvas'
import { getConfigKey, getUserInfo, loginOut } from '@/api/user'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { getToken, removeToken } from '@/store/modules/auth/helper'
import { useChatStore } from '@/store'
import { useUserStore } from '@/store/modules/user'
import { useTenantStore } from '@/store/modules/tenant'
import { PromptStore } from '@/components/common'
import { router } from '@/router'
const { isMobile } = useBasicLayout()

const chatStore = useChatStore()
// import gallery from '@/views/gallery/index.vue'

const Setting = defineAsyncComponent(
  () => import('@/components/common/Setting/index.vue'),
)

const qrCodeData = ref('')
const st = ref({ show: false, showImg: false, menu: [], active: 'chat' })
const router1 = useRouter()
// const userInfo = ref<UserInfo>(defaultSetting().userInfo)
const message = useMessage()
const show = ref(false)
const shareshow = ref(false)
const urouter = useRouter() //
const isLogin = computed(() => {
  return localStorage.getItem('TOKEN')
})

const goHome = computed(() => {
  return router.currentRoute.value.name
})

const userStore = useUserStore()
const tenantStore = useTenantStore()
const userInfo = computed(() => userStore.userInfo)
const homeLogo = computed(() => tenantStore.getHomeLogo)
onMounted(() => {
  // getLogo()
  getLoginUserInfo()
})

/**
 * 获取默认头像
 */
async function getLogo() {
  const [err1, res1] = await to(getConfigKey('logoImage'))
  if (err1)
    console.error('获取配置失败', err1.message)
  else userInfo.value.avatar = res1.msg
}

/**
 * 获取当前登录用户信息
 */
async function getLoginUserInfo() {
  // 用户未登录,不需要获取用户信息
  if (!getToken())
    return

  const [err, newUserInfo] = await to(getUserInfo())
  if (err) {
    // message.error(err.toString())
    console.log(err.toString())
  }
  console.log('newUserInfo==  aiHeader.vue', newUserInfo)
  if (newUserInfo) {
    const userStore = useUserStore()
    userStore.updateUserInfo(newUserInfo.data.user)
    userStore.updateUserRoles(newUserInfo.data.roles)
    userInfo.value = newUserInfo.data.user
    qrCodeData.value = `https://aih5.fitkc.cn?sid=${newUserInfo.data.user.userName}`
  }
}

const chatId = computed(() => chatStore.active ?? '1002')
/**
 * 退出登录
 */
async function handleReset() {
  await loginOut()
  // 删除用户token
  removeToken()
  // 跳转到登录页面
  router1.push('/login')
}

async function longin() {
  // 跳转到登录页面
  router1.push('/login')
}

function renderIcon(icon: Component) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

const menuOptions = ref([
  {
    label: '分享',
    key: 'share',
    icon: renderIcon(qrcode),
  },
  {
    label: '账号设置',
    key: 'accountSettings',
    icon: renderIcon(settings),
  },
  {
    label: '购买套餐',
    key: 'buy',
    icon: renderIcon(storefront),
  },
  {
    label: '退出账号',
    key: 'logout',
    icon: renderIcon(out),
  },
])

const handleSelect = (key: string) => {
  if (key === 'accountSettings') {
    st.value.show = true
  }
  else if (key === 'logout') {
    handleReset()
  }
  else if (key === 'buy') {
    // st.value.show = true
    show.value = true
  }
  else if (key === 'share') {
    shareshow.value = true
  }
}
const onNegativeClick = () => {
  shareshow.value = false
}
// const onPositiveClick = () => {
//   message.success('Submit')
//

/**
 * 生成二维码分享区域的截图并下载
 */
async function downloadQRCode() {
  const shareElement = document.querySelector('.qr-code-share')
  if (shareElement) {
    try {
      const canvas = await html2canvas(shareElement)
      const link = document.createElement('a')
      link.download = 'FIT飞地科服AI智脑-https://aiweb.fitkc.cn.png'
      link.href = canvas.toDataURL('image/png')
      link.click()
    }
    catch (error) {
      message.error('二维码下载失败，请重试')
      console.error('生成二维码截图失败:', error)
    }
  }
}

const onPositiveClick = () => {
  downloadQRCode()
  shareshow.value = false
}
</script>

<template>
  <NLayoutHeader style="height: 56px; line-height: 56px; background: #ccc" bordered>
    <div class="flex flex-row theme-bg-color">
      <div class="pl-3 font-bold" style="height: 56px">
        <img
          v-if="homeLogo"
          :src="homeLogo"
          alt=""
          style="max-height: 26px; margin-top: 17px; cursor: pointer; width: 220px"
          @click="router1.push('/dashboard')"
        >
        <img
          v-else
          src="@/assets/img/logo.png"
          alt=""
          style="max-height: 26px; margin-top: 17px; cursor: pointer; width: 220px"
          @click="router1.push('/dashboard')"
        >
      </div>
      <div class="header-right">
        <NMenu
          mode="horizontal"
          :options="menuOptions"
          responsive
          @select="handleSelect"
        />

        <div class="flex items-center text-right pr-4 ml-4">
          <NAvatar
            v-show="isLogin"
            :size="32"
            round
            :src="userInfo.avatar"
            class="align-middle"
            fallback-src="/src/assets/img/profile.png"
          />
          <span class="ml-2 whitespace-nowrap">
            {{ userInfo.nickName || userInfo.userName }}</span>
        </div>
        <div v-show="!isLogin" class="user-bottom" @click="longin">
          <NButton tertiary type="info">
            登录
          </NButton>
        </div>
      </div>
    </div>
  </NLayoutHeader>
  <!-- <div class="flex-shrink-0 w-[170px] z-[1000] h-full" data-tauri-drag-region>
    <div
      class="flex h-full select-none flex-col items-center justify-between bg-[#e8eaf1] px-2 pt-4 pb-8 dark:bg-[#25272d]"
    >
      <div class="flex flex-col space-y-2">
        <NPopover trigger="click" :show-arrow="false">
          <template #trigger>
            <NAvatar v-show="isLogin" size="large" round :src="userInfo.avatar" />
          </template>
          <NMenu :options="menuOptions" @select="handleSelect" />
        </NPopover>

        <div v-show="!isLogin" class="user-bottom" @click="longin">
          <n-button tertiary type="info">
            登录
          </n-button>
        </div>
      </div>
    </div>
  </div> -->
  <Setting v-if="st.show" v-model:visible="st.show" />
  <PromptStore v-model:visible="show" />
  <NModal
    v-model:show="shareshow"
    preset="dialog"
    title="分享"
    positive-text="下载二维码"
    negative-text="取消"
    @positive-click="onPositiveClick"
    @negative-click="onNegativeClick"
  >
    <div class="qr-code-share">
      <div class="qr-code">
        <VueQrcode v-model:value="qrCodeData" size="120" />
      </div>
      <div class="qr-code-title text-base">
        {{ userInfo.nickName || userInfo.userName }}邀请您一起体验FIT飞地科服AI智脑
      </div>
    </div>
  </NModal>
</template>

<style lang="less" scoped>
.header-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: absolute;
  right: 0;
  line-height: 56px;
}
.qr-code-share {
  text-align: center;
  padding: 15px 0;
  .qr-code {
    border: 1px solid #dfdfdf;
    padding: 12px;
    border-radius: 7px;
    width: auto;
    display: inline-block;
  }
}

// ::v-deep .n-menu-item {
//   .n-menu-item-content--selected {
//     .n-menu-item-content__icon,
//     .n-menu-item-content-header {
//       color: #2254f4 !important;
//     }
//   }
// }
</style>
