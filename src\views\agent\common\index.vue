<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Datasets from './datasets.vue'
import DifyChat from './../../dify/layout/index.vue'
import cache from '@/plugins/cache'
const router = useRouter()
const route = useRoute()
const src = ref('')
const code = ref('')
const type = route.query.type

onMounted(() => {
  // src.value = decodeURIComponent(
  //   route.query.href || 'https://k.fitkc.cn/chat/NWL7LnV2gGy65pxw',
  // )
  let baseUrl = ''
  if (type === 'k')
    baseUrl = 'https://k.fitkc.cn/chat/'
  else if (type === 'agent')
    baseUrl = 'https://k.fitkc.cn/chat/'
  else if (type === 'completion')
    baseUrl = 'https://k.fitkc.cn/completion/'

  code.value = decodeURIComponent(route.query.code)
  src.value = `${baseUrl}${code.value}`
})
</script>

<template>
  <div v-if="code" style="background-color: #eee">
    <Datasets />
    <DifyChat v-if="cache.session.get('appKey')" />
    <iframe
      v-else
      id="assessId"
      :src="src"
      width="100%"
      frameborder="0"
      allow="microphone"
      class="iframe-wrapper"
    />
  </div>
</template>

<style lang="less" scoped>
.iframe-wrapper {
  height: calc(100vh - 56px);
}
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}
</style>
