<script setup lang="ts">
import type { Component } from 'vue'
import { computed, defineAsyncComponent, h, onMounted, ref } from 'vue'
import { NIcon, useMessage } from 'naive-ui'
import { useRouter } from 'vue-router'
import to from 'await-to-js'
import {
  LogOut as out,
  Settings as settings,
  Storefront as storefront,
} from '@vicons/ionicons5'
import { getConfigKey, getRouters, getUserInfo, loginOut } from '@/api/user'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { getToken, removeToken } from '@/store/modules/auth/helper'
import { useChatStore } from '@/store'
import { useAppStore } from '@/store/modules/app'
import type { UserInfo } from '@/store/modules/user/helper'
import { defaultSetting } from '@/store/modules/user/helper'

import { router } from '@/router'
const { isMobile } = useBasicLayout()

const chatStore = useChatStore()
const appStore = useAppStore()
// import gallery from '@/views/gallery/index.vue'

const Setting = defineAsyncComponent(
  () => import('@/components/common/Setting/index.vue'),
)

const st = ref({ show: false, showImg: false, menu: [], active: 'dashboard' })
const router1 = useRouter()
const userInfo = ref<UserInfo>(defaultSetting().userInfo)
const message = useMessage()
const show = ref(false)
const urouter = useRouter() //
const isLogin = computed(() => {
  return localStorage.getItem('TOKEN')
})

const goHome = computed(() => {
  return router.currentRoute.value.name
})

/**
 * 获取默认头像
 */
async function getLogo() {
  const [err1, res1] = await to(getConfigKey('logoImage'))
  if (err1)
    console.error('获取配置失败', err1.message)
  else userInfo.value.avatar = res1.msg
}

const getMenu = async () => {
  const [err, res] = await to(getRouters())
  const child = res.data.find((item: any) => item.name === 'App')?.children
  list.value = child.filter((item: any) => !item.hidden)
  const toPath = router.currentRoute.value.name
  const item = list.value.find((item: any) => item.component === toPath)
  if (item) {
    st.value.active = item.component
    appStore.setSubMenuData(item.children || [])
  }

  if (err)
    console.error('获取菜单失败', err.message)
}

/**
 * 获取当前登录用户信息
 */
async function getLoginUserInfo() {
  // 用户未登录,不需要获取用户信息
  if (!getToken())
    return

  const [err, newUserInfo] = await to(getUserInfo())
  if (err) {
    // message.error(err.toString())
    console.log(err.toString())
  }
  if (newUserInfo) {
    if (newUserInfo.data.user.avatar)
      userInfo.value.avatar = newUserInfo.data.user.avatar

    userInfo.value.name = newUserInfo.data.user.nickName
    userInfo.value.userBalance = newUserInfo.data.user.userBalance
    userInfo.value.userName = newUserInfo.data.user.userName
    isLogin.value = true
  }
}

const chatId = computed(() => chatStore.active ?? '1002')
/**
 * 退出登录
 */
async function handleReset() {
  await loginOut()
  // 删除用户token
  removeToken()
  // 跳转到登录页面
  router1.push('/login')
}

async function longin() {
  // 跳转到登录页面
  router1.push('/login')
}

function renderIcon(icon: Component) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

const menuOptions = ref([
  {
    label: '账号设置',
    key: 'accountSettings',
    icon: renderIcon(settings),
  },
  {
    label: '购买套餐',
    key: 'buy',
    icon: renderIcon(storefront),
  },
  {
    label: '退出账号',
    key: 'logout',
    icon: renderIcon(out),
  },
])

const list = ref([])

const handleSelect = (key: string) => {
  if (key === 'accountSettings')
    st.value.show = true
  else if (key === 'logout')
    handleReset()
  else if (key === 'buy')
    show.value = true
}
const clickMenu = (item: any) => {
  if (st.value.active !== item.component) {
    st.value.active = item.component
    if (item.query) {
      urouter.push({
        path: `/${item.path}`,
        query: JSON.parse(item.query),
      })
    }
    else {
      urouter.push(`/${item.path}`)
    }
    appStore.setSubMenuData(item.children || [])
  }
}
onMounted(() => {
  // getLogo()
  // getLoginUserInfo()
  getMenu()
  return () => {
    unregister()
  }
})
</script>

<template>
  <div
    v-if="!isMobile"
    class="flex-shrink-0 w-[140px] z-[1000] h-full ai-sider-wrapper"
    data-tauri-drag-region
  >
    <div
      class="flex overflow-auto select-none flex-col items-center justify-between bg-[#EDF0FF] px-2 pt-4 pb-8 dark:bg-[#EDF0FF]"
      style="height: calc(100vh - 56px)"
    >
      <div class="flex flex-col space-y-2 flex-1" data-tauri-drag-region>
        <a
          v-for="(item, index) in list"
          :key="index"
          class="router-link-active router-link-exact-active cursor-pointer duration-300 dark:bg-[#34373c]"
          @click="clickMenu(item)"
        >
          <div
            class="flex h-full justify-center items-center py-1 flex-col"
            :class="[goHome === item.component ? 'active' : '']"
          >
            <div
              class="items-icon mb-1"
              :class="[goHome === item.component ? 'active' : '']"
            >
              <i class="iconfont" :class="[item.meta.icon]" />
            </div>
            {{ item.meta.title }}
          </div>
        </a>

        <!-- <a
          class="router-link-active router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'chat';
            urouter.push(`/chat`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'Chat' ? 'active' : '']"
              >
                <SvgIcon icon="ri:wechat-line" class="text-2xl flex-1" />
              </div>
            </template>
            对话
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'store';
            urouter.push(`/store`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'store' ? 'active' : '']"
              >
                <SvgIcon icon="ri:apps-fill" class="text-2xl flex-1" />
              </div>
            </template>
            应用中心
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'knowledge1';
            urouter.push(`/knowledge`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'knowledge1' ? 'active' : '']"
              >
                <SvgIcon icon="garden:knowledge-base-26" class="text-2xl flex-1" />
              </div>
            </template>
            知识库
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'draw';
            urouter.push(`/draw`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'draw' ? 'active' : '']"
              >
                <SvgIcon icon="material-symbols:draw-rounded" class="text-2xl flex-1" />
              </div>
            </template>
            绘画
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'music';
            urouter.push(`/music`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'music' ? 'active' : '']"
              >
                <SvgIcon icon="mingcute:music-line" class="text-2xl flex-1" />
              </div>
            </template>
            音乐
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'video';
            urouter.push(`/video`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'video' ? 'active' : '']"
              >
                <SvgIcon icon="mynaui:video" class="text-2xl flex-1" />
              </div>
            </template>
            视频
          </NTooltip>
        </a>

        <a
          class="router-link-exact-active h-10 w-10 cursor-pointer rounded-xl bg-white duration-300 dark:bg-[#34373c] hover:bg-[#bbb] dark:hover:bg-[#555]"
          @click="
            st.active = 'ppt';
            urouter.push(`/ppt`);
          "
        >
          <NTooltip placement="right" trigger="hover">
            <template #trigger>
              <div
                class="flex h-full justify-center items-center py-1 flex-col"
                :class="[goHome === 'ppt' ? 'active' : '']"
              >
                <SvgIcon icon="icon-park-outline:ppt" class="text-2xl flex-1" />
              </div>
            </template>
            PPT
          </NTooltip>
        </a>
      </div> -->

        <!-- <div class="flex flex-col space-y-2">
        <NPopover trigger="click" :show-arrow="false">
          <template #trigger>
            <NAvatar v-show="isLogin" size="large" round :src="userInfo.avatar" />
          </template>
          <NMenu :options="menuOptions" @select="handleSelect" />
        </NPopover>

        <div v-show="!isLogin" class="user-bottom" @click="longin">
          <n-button tertiary type="info">
            登录
          </n-button>
        </div>
      </div> -->
      </div>
    </div>
    <Setting v-if="st.show" v-model:visible="st.show" />
    <PromptStore v-model:visible="show" />
  </div>
</template>

<style lang="less">
.ai-sider-wrapper {
  a {
    > div {
      min-width: 105px;
      &:hover {
        background: #fff;
        border-radius: 8px;
      }
    }
    > div.active {
      background: #fff;
      border-radius: 8px;
    }
  }
  .items-icon {
    &.active {
      background: linear-gradient(-45deg, #2254f4, #6a8dff);
      border-radius: 8px;
      i {
        color: #fff !important;
      }
    }
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #e8eff4;
    border-radius: 8px;
    margin-top: 5px;
    i {
      font-size: 24px;
    }
  }
}
</style>
