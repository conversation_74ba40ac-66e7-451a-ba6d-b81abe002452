<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { NButton, NImage, NModal, NSpin, useMessage } from 'naive-ui'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { SvgIcon } from '@/components/common'

interface Props {
  visible: boolean
  orderInfo: {
    money: string
    name: string
    description?: string
    id?: string | number
  }
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const { isMobile } = useBasicLayout()
const message = useMessage()

// 支付相关状态
const paymentMethod = ref('wechat')
const paymentStep = ref(1) // 1-选择支付方式，2-显示二维码
const loading = ref(false)
const confirmLoading = ref(false)
const qrCodeUrl = ref('')

const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})

// 重置状态
const resetState = () => {
  paymentStep.value = 1
  paymentMethod.value = 'wechat'
  qrCodeUrl.value = ''
  loading.value = false
  confirmLoading.value = false
}

// 监听弹窗关闭
watch(show, (newValue) => {
  if (!newValue)
    resetState()
})

// 开始支付
const handleStartPayment = async () => {
  loading.value = true

  try {
    if (paymentMethod.value === 'wechat') {
      // 模拟获取二维码
      await new Promise(resolve => setTimeout(resolve, 1000))
      qrCodeUrl.value = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIiBzdHJva2U9IiNkZGQiIHN0cm9rZS13aWR0aD0iMiIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNDAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuaUr+S7mOS6jOe7tOeggeWNoOS9jeWbvjwvdGV4dD4KICA8dGV4dCB4PSI1MCUiIHk9IjYwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjYmJiIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ov7flsIbmiJHlt7Lmlq/ku5jvvIE8L3RleHQ+Cjwvc3ZnPg=='
      paymentStep.value = 2
      message.success('支付二维码已生成（演示模式）')
    }
    else {
      // 支付宝支付
      message.info('支付宝支付功能已触发（演示模式）')
      show.value = false
      emit('success')
    }
  }
  catch (error) {
    message.error('支付请求失败')
  }
  finally {
    loading.value = false
  }
}

// 确认支付
const handleConfirmPayment = async () => {
  confirmLoading.value = true

  // 模拟支付确认
  await new Promise(resolve => setTimeout(resolve, 1500))

  message.success('支付成功！感谢您的购买（演示模式）')
  confirmLoading.value = false
  show.value = false
  emit('success')
}

// 返回选择支付方式
const handleBack = () => {
  paymentStep.value = 1
  qrCodeUrl.value = ''
}

// 取消支付
const handleCancel = () => {
  show.value = false
  emit('cancel')
}
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    :closable="true"
    :mask-closable="false"
    :style="{
      width: isMobile ? '95vw' : '800px',
      maxWidth: '95vw',
    }"
    class="premium-payment-modal"
    @close="handleCancel"
  >
    <template #header>
      <div class="modal-header">
        <SvgIcon icon="mdi:diamond-stone" class="text-[24px] text-blue-500" />
        <span class="header-title">购买套餐</span>
      </div>
    </template>

    <div class="payment-container">
      <!-- 横向布局：左侧商品信息，右侧支付方式 -->
      <div class="payment-content">
        <!-- 左侧：商品信息 -->
        <div class="product-section">
          <div class="product-info">
            <h3 class="product-name">
              {{ orderInfo.name }}
            </h3>
            <div class="product-price">
              <span class="currency">¥</span>
              <span class="amount">{{ orderInfo.money }}</span>
            </div>
            <div class="product-description">
              {{ orderInfo.description || '专业AI套餐服务，享受更多功能' }}
            </div>
            <div class="product-features">
              <div class="feature-item">
                <SvgIcon icon="mdi:check-circle" class="text-[16px] text-green-500" />
                <span>专业AI模型访问</span>
              </div>
              <div class="feature-item">
                <SvgIcon icon="mdi:check-circle" class="text-[16px] text-green-500" />
                <span>无限制对话次数</span>
              </div>
              <div class="feature-item">
                <SvgIcon icon="mdi:check-circle" class="text-[16px] text-green-500" />
                <span>优先客服支持</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：支付方式 -->
        <div class="payment-section">
          <!-- 支付方式选择 -->
          <div v-if="paymentStep === 1" class="payment-methods">
            <h4 class="section-title">
              选择支付方式
            </h4>
            <div class="payment-options">
              <div
                class="payment-option" :class="[{ active: paymentMethod === 'wechat' }]"
                @click="paymentMethod = 'wechat'"
              >
                <div class="option-icon wechat">
                  <SvgIcon icon="ri:wechat-pay-line" class="text-[24px]" />
                </div>
                <div class="option-content">
                  <div class="option-name">
                    微信支付
                  </div>
                  <div class="option-desc">
                    使用微信扫码支付
                  </div>
                </div>
                <div class="option-radio">
                  <div class="radio-circle" :class="[{ checked: paymentMethod === 'wechat' }]">
                    <div v-if="paymentMethod === 'wechat'" class="radio-dot" />
                  </div>
                </div>
              </div>

              <div
                class="payment-option" :class="[{ active: paymentMethod === 'alipay' }]"
                @click="paymentMethod = 'alipay'"
              >
                <div class="option-icon alipay">
                  <SvgIcon icon="ri:alipay-line" class="text-[24px]" />
                </div>
                <div class="option-content">
                  <div class="option-name">
                    支付宝
                  </div>
                  <div class="option-desc">
                    跳转支付宝完成支付
                  </div>
                </div>
                <div class="option-radio">
                  <div class="radio-circle" :class="[{ checked: paymentMethod === 'alipay' }]">
                    <div v-if="paymentMethod === 'alipay'" class="radio-dot" />
                  </div>
                </div>
              </div>
            </div>

            <div class="payment-action">
              <NButton
                type="primary"
                size="large"
                :loading="loading"
                class="pay-button"
                block
                @click="handleStartPayment"
              >
                立即支付
              </NButton>
            </div>
          </div>

          <!-- 二维码支付界面 -->
          <div v-else-if="paymentStep === 2" class="qrcode-section">
            <div class="qrcode-header">
              <h4 class="section-title">
                扫码支付
              </h4>
              <p class="qrcode-subtitle">
                请使用微信扫描二维码完成支付
              </p>
            </div>

            <div class="qrcode-container">
              <div class="qrcode-wrapper">
                <NImage
                  :src="qrCodeUrl"
                  width="180"
                  height="180"
                  class="qrcode-image"
                  :preview-disabled="true"
                />
                <div v-if="loading" class="qrcode-overlay">
                  <NSpin size="large" />
                </div>
              </div>

              <div class="payment-amount">
                <div class="amount-text">
                  支付金额
                </div>
                <div class="amount-price">
                  <span class="currency">¥</span>
                  <span class="price">{{ orderInfo.money }}</span>
                </div>
              </div>
            </div>

            <div class="qrcode-actions">
              <NButton
                class="back-button"
                size="large"
                @click="handleBack"
              >
                返回
              </NButton>
              <NButton
                type="primary"
                size="large"
                :loading="confirmLoading"
                class="confirm-button"
                @click="handleConfirmPayment"
              >
                我已支付
              </NButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.premium-payment-modal {
  --primary-color: #1677ff;
  --success-color: #52c41a;
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --border-color: #e8e8e8;
  --bg-color: #fafafa;
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

/* 主要内容区域 */
.payment-container {
  padding: 0;
}

.payment-content {
  display: flex;
  gap: 32px;
  min-height: 400px;
}

/* 左侧商品信息 */
.product-section {
  flex: 1;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.product-info {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 12px 0;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 16px;
}

.currency {
  font-size: 18px;
  color: #ff4d4f;
  font-weight: 600;
  margin-right: 4px;
}

.amount {
  font-size: 32px;
  color: #ff4d4f;
  font-weight: 700;
}

.product-description {
  color: var(--text-color-secondary);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.product-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-color);
}

/* 右侧支付区域 */
.payment-section {
  flex: 1;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 20px 0;
}

/* 支付方式选择 */
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.payment-option:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
}

.payment-option.active {
  border-color: var(--primary-color);
  background: rgba(22, 119, 255, 0.02);
}

.option-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 6px;
}

.option-icon.wechat {
  background: rgba(7, 193, 96, 0.1);
  color: #07C160;
}

.option-icon.alipay {
  background: rgba(22, 119, 255, 0.1);
  color: #1677FF;
}

.option-content {
  flex: 1;
}

.option-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2px;
}

.option-desc {
  font-size: 13px;
  color: var(--text-color-secondary);
}

.option-radio {
  margin-left: 12px;
}

.radio-circle {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.radio-circle.checked {
  border-color: var(--primary-color);
}

.radio-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
}

/* 支付按钮 */
.payment-action {
  margin-top: 24px;
}

.pay-button {
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
  border: none;
}

/* 二维码支付区域 */
.qrcode-section {
  text-align: center;
}

.qrcode-subtitle {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin: 0 0 24px 0;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
}

.qrcode-wrapper {
  position: relative;
  padding: 16px;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.qrcode-image {
  display: block;
}

.qrcode-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.payment-amount {
  text-align: center;
}

.amount-text {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin-bottom: 8px;
}

.amount-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  color: #ff4d4f;
  font-weight: 600;
}

.amount-price .currency {
  font-size: 16px;
  margin-right: 2px;
}

.amount-price .price {
  font-size: 24px;
}

/* 二维码页面按钮 */
.qrcode-actions {
  display: flex;
  gap: 12px;
}

.back-button,
.confirm-button {
  flex: 1;
  height: 40px;
  font-size: 14px;
}

.back-button {
  background: white;
  border: 1px solid var(--border-color);
  color: var(--text-color-secondary);
}

.confirm-button {
  background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
  border: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .payment-content {
    flex-direction: column;
    gap: 16px;
  }

  .product-section,
  .payment-section {
    padding: 16px;
  }

  .product-name {
    font-size: 18px;
  }

  .amount {
    font-size: 28px;
  }

  .payment-option {
    padding: 12px;
  }

  .option-icon {
    width: 36px;
    height: 36px;
  }

  .option-name {
    font-size: 14px;
  }

  .option-desc {
    font-size: 12px;
  }

  .qrcode-wrapper {
    padding: 12px;
  }
}
</style>
